from playwright.async_api import async_playwright

headless = False  # Change to True to run the browser in headless mode

# Launch and keep browser running
p = await async_playwright().start()
browser = await p.chromium.launch(headless=headless)
# context is the browser window
context = await browser.new_context()
# page is the tab in the browser
page = await context.new_page()
print("Browser launched!")

await page.goto("https://www.bing.com")
print("Navigated to Bing homepage")

from IPython.display import Image, display

# Take a screenshot and store it in memory
screenshot_bytes = await page.screenshot()

# Display the screenshot
display(Image(screenshot_bytes))

# Get all the text on the page and print first 10 lines
text = await page.evaluate("() => document.body.innerText")
print("\nFirst 10 lines of text content:")
print("\n".join(text.split("\n")[:10]))

from dataclasses import dataclass
from playwright.async_api import Page


# A class to represent an interactive element on the page
@dataclass
class Element:
    id: int  # The id of the element
    aria_label: (
        str  # The aria-label attribute is used to provide a label for an element
    )
    type: str  # The type of the element
    bbox: dict  # The bounding box of the element
    text: str  # The text content of the element


# We will now go over the page and extract all interactive elements
# We will also add a data attribute to the element with the element ID for later reference
async def get_interactive_elements(page: Page) -> list[Element]:
    elements: list[Element] = []
    # Viewport size is a dict with keys 'width' and 'height'
    viewport_size = page.viewport_size
    print(f"Viewport size: {viewport_size}")

    # For simplicity, we will only look at buttons, textboxes, and links. We can add more roles later on.
    interactive_roles = ["button", "textbox", "link"]
    i = 0
    for role in interactive_roles:
        print(f"Getting {role} elements...")
        # We will use the Playwright API to get all elements with the given role
        elements_with_role = await page.get_by_role(role).all()
        for element in elements_with_role:
            # Check if element is visible and in current viewport
            bbox = await element.bounding_box()
            if bbox:  # Element is visible if it has a bounding box
                # Check if element is in current viewport (not scrolled out of view)
                if 0 <= bbox["y"] <= viewport_size["height"]:
                    # Set a data attribute with the element ID for later reference
                    await element.evaluate(f"el => el.setAttribute('data-element-id', '{i}')")
                    elements.append(
                        Element(
                            id=i,
                            aria_label=await element.get_attribute("aria-label")
                            or await element.get_attribute("aria-role")
                            or "",
                            type=role,
                            bbox=bbox,
                            text=await element.text_content() or "",
                        )
                    )
                    i += 1
    print(f"Found {len(elements)} visible interactive elements in current viewport:")
    return elements


elements = await get_interactive_elements(page)
formatted_list_of_elements = "\n".join(
    [f"Element {i}: {element}" for i, element in enumerate(elements)]
)
print(formatted_list_of_elements)

# find the search box
search_box_id = None
for element in elements:
    if element.type == "textbox":
        search_box_id = element.id
        break
print(f"Search box id: {search_box_id}")

from PIL import Image, ImageDraw
import io


def get_som_screenshot(screenshot_bytes: bytes, elements: list[Element]) -> Image.Image:
    screenshot = Image.open(io.BytesIO(screenshot_bytes))

    # Create a drawing object
    draw = ImageDraw.Draw(screenshot)

    # Draw bounding boxes and element IDs for each element
    for element in elements:
        bbox = element.bbox
        x = bbox["x"]
        y = bbox["y"]
        width = bbox["width"]
        height = bbox["height"]

        # Draw rectangle
        draw.rectangle([(x, y), (x + width, y + height)], outline="red", width=2)

        # Draw element ID
        draw.text((x, y - 15), f"{element.id}", fill="red")

    # Display the annotated screenshot
    display(screenshot)
    som_screenshot = screenshot.copy()
    return som_screenshot


screenshot_bytes = await page.screenshot()
som_screenshot = get_som_screenshot(screenshot_bytes, elements)

async def prepare_page_for_agent(page: Page) -> tuple[str, str, Image.Image]:
    """
    Prepare the page for the agent.
    Returns:
        tuple[str, str, Image.Image]: The page text, the formatted list of elements, and the screenshot with bounding boxes.
    """
    page_text = await page.evaluate("() => document.body.innerText")
    elements = await get_interactive_elements(page)
    screenshot_bytes = await page.screenshot()
    som_screenshot = get_som_screenshot(screenshot_bytes, elements)

    formatted_list_of_elements = "\n".join(
        [f"Element {i}: {element}" for i, element in enumerate(elements)]
    )

    return page_text, formatted_list_of_elements, som_screenshot


# page_text, formatted_list_of_elements, screenshot = await prepare_page_for_agent(page)

AGENT_PROMPT = """
You are a helpful assistant that can navigate a web page and perform actions on it.

The task we are trying to complete is:
{task}

The current visible text on the page is:
{page_text}

The current visible elements on the page are:
{formatted_list_of_elements}

You will need to decide on the next action to take.

The action space is:
- goto(url): navigate to a URL
- click(id): click a button given it's ID
- type(id, text): type "text" into element "id"
- scroll(direction): scroll the page in direction up or down.
- stop_action(final_answer): declare that we have finished the task and prepare a final_answer to return to the user.

Output a JSON object with the following fields:
{{
    "action": "goto" | "click" | "type" | "scroll" | "stop_action",
    "action_args": {{
        "url": "https://www.google.com",
        "id": "123",
        "text": "Hello",
        "direction": "up"
    }}
}}

Only output the JSON object, no other text or comments.
"""

from openai import OpenAI
import json
import base64
from PIL import Image
import os

# Prepare the page for the agent
page_text, formatted_list_of_elements, som_screenshot = await prepare_page_for_agent(
    page
)
task = "Search for Magentic-UI"
# Now make the API call
client = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY")
)  # you can use any other LLM client here
image_data_url = f"data:image/png;base64,{base64.b64encode((lambda b: (som_screenshot.save(b, format='PNG'), b.getvalue())[1])(io.BytesIO())).decode()}"


def get_llm_response(
    client: OpenAI,  # OpenAI client
    task: str,  # Task to complete
    page_text: str,  # Page text
    formatted_list_of_elements: str,  # Formatted list of elements
    image_data_url: str,  # Image data URL
    message_history: list[dict] = [],  # Message history
    model: str = "gpt-4o",  # Model to use
) -> dict:
    response = client.chat.completions.create(
        model=model,
        messages=[
            *message_history,
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": AGENT_PROMPT.format(
                            task=task,
                            page_text=page_text,
                            formatted_list_of_elements=formatted_list_of_elements,
                        ),
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": image_data_url},
                    },
                ],
            },
        ],
    )

    # Parse the response
    try:
        action_decision = json.loads(response.choices[0].message.content)
        print("Model's decision:", json.dumps(action_decision, indent=2))
    except json.JSONDecodeError:
        # it starts with ```json
        response_content = response.choices[0].message.content
        response_content = response_content.replace("```json", "").replace("```", "")
        action_decision = json.loads(response_content)
        print("Model's decision:", json.dumps(action_decision, indent=2))
    except Exception as e:
        raise e
    return action_decision


action_decision = get_llm_response(
    client, task, page_text, formatted_list_of_elements, image_data_url
)
print(action_decision)

# This is mostly basic Playwright code, but we will use it to execute the actions.
async def execute_action(action: str, action_args: dict, page: Page) -> str:
    """
    Execute an action on the page.
    """
    if action == "goto":
        await page.goto(action_args["url"])
        return f"I navigated to {action_args['url']}"
    elif action == "click":
        # Get the element using the data attribute
        await page.wait_for_selector(f"[data-element-id='{action_args['id']}']")
        element = page.locator(f"[data-element-id='{action_args['id']}']")
        if element:
            await element.click()
        else:
            raise ValueError(f"Element with ID {action_args['id']} not found")
        return f"I clicked on {action_args['id']}"
    elif action == "type":
        await page.wait_for_selector(f"[data-element-id='{action_args['id']}']")
        element = page.locator(f"[data-element-id='{action_args['id']}']")
        if element:
            await element.fill(action_args["text"])
        else:
            raise ValueError(f"Element with ID {action_args['id']} not found")
        return f"I typed {action_args['text']} into {action_args['id']}"
    elif action == "scroll":
        await page.scroll(action_args["direction"])
        return f"I scrolled {action_args['direction']}"
    elif action == "stop_action":
        return action_args["final_answer"]
    else:
        raise ValueError(f"Invalid action: {action}")

await execute_action(action_decision["action"], action_decision["action_args"], page)

# Take a screenshot of the page
screenshot = await page.screenshot()
display(Image.open(io.BytesIO(screenshot)))

await browser.close()

from openai import OpenAI
from playwright.async_api import Page
from playwright.async_api import async_playwright
from PIL import Image, ImageDraw
import io
import base64
import json
from dataclasses import dataclass
from IPython.display import display

@dataclass
class Element:
    id: int  # The id of the element
    aria_label: (
        str  # The aria-label attribute is used to provide a label for an element
    )
    type: str  # The type of the element
    bbox: dict  # The bounding box of the element
    text: str  # The text content of the element


AGENT_PROMPT = """
You are a helpful assistant that can navigate a web page and perform actions on it.

The task we are trying to complete is:
{task}

The current visible text on the page is:
{page_text}

The current visible elements on the page are:
{formatted_list_of_elements}

You will need to decide on the next action to take.

The action space is:
- goto(url): navigate to a URL
- click(id): click a button given it's ID
- type(id, text): type "text" into element "id"
- scroll(direction): scroll the page in direction up or down.
- stop_action(final_answer): declare that we have finished the task and prepare a final_answer to return to the user.

Output a JSON object with the following fields:
{{
    "action": "goto" | "click" | "type" | "scroll" | "stop_action",
    "action_args": {{
        "url": "https://www.google.com",
        "id": "123",
        "text": "Hello",
        "direction": "up"
    }}
}}

Only output the JSON object, no other text or comments.
"""


class BrowserUseAgent:
    def __init__(
        self,
        client: OpenAI,
        model: str = "gpt-4o",
        headless: bool = False,
        run_in_jupyter: bool = True,
    ):
        self.client = client
        self.model = model
        self.headless = headless
        self.message_history: list[dict] = []
        self.page: Page = None
        self.run_in_jupyter = run_in_jupyter

    async def _launch_browser(self) -> None:
        p = await async_playwright().start()
        self.browser = await p.chromium.launch(headless=self.headless)
        # context is the browser window
        self.context = await self.browser.new_context()
        # page is the tab in the browser
        self.page = await self.context.new_page()

    async def execute_task(self, task: str) -> str:
        """
        This is NEW! This is the main function that will be called to execute the task and implement our agent loop.
        """
        # Step 1: Launch the browser if it's not already launched
        if self.page is None:
            await self._launch_browser()
        # Our stop condition is when the LLM decides to output stop_action
        should_stop = False
        final_answer = None
        i = 0
        while not should_stop:
            # Step 2: Prepare the page for the agent
            (
                page_text,
                formatted_list_of_elements,
                som_screenshot,
            ) = await self._prepare_page_for_agent(self.page)
            # Step 3: Get the LLM response
            image_data_url = f"data:image/png;base64,{base64.b64encode((lambda b: (som_screenshot.save(b, format='PNG'), b.getvalue())[1])(io.BytesIO())).decode()}"
            action_decision = self._get_llm_response(
                self.client,
                task,
                page_text,
                formatted_list_of_elements,
                image_data_url,
                self.message_history,
                self.model,
            )
            print(f"Action decision {i}: {action_decision}")
            # Add the action decision to the message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": [{"type": "text", "text": json.dumps(action_decision)}],
                }
            )
            # Step 4: Execute the action with some error handling
            try:
                action_feedback = await self._execute_action(
                    action_decision["action"], action_decision["action_args"], self.page
                )
            except Exception as e:
                print(f"Error executing action {i}: {e}")
                action_feedback = f"Error executing action {i}: {e}"
            print(f"Action feedback {i}: {action_feedback}")
            # Sleep for 3 seconds to let the page load
            await self.page.wait_for_timeout(3000)
            # Update the message history with feedback on the action and the new page screenshot
            new_page_screenshot = await self.page.screenshot()
            self.message_history.append(
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": action_feedback},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64.b64encode(new_page_screenshot).decode()}"
                            },
                        },
                    ],
                }
            )
            if self.run_in_jupyter:
                display(Image.open(io.BytesIO(new_page_screenshot)))
            # Check if the task is complete
            should_stop = action_decision["action"] == "stop_action"
            if should_stop:
                final_answer = action_decision["action_args"]["final_answer"]
            i += 1
        return final_answer

    async def _execute_action(self, action: str, action_args: dict, page: Page) -> str:
        """
        Execute an action on the page.
        """
        if action == "goto":
            await page.goto(action_args["url"])
            return f"I navigated to {action_args['url']}"
        elif action == "click":
            # Get the element using the data attribute
            await page.wait_for_selector(f"[data-element-id='{action_args['id']}']")
            element = page.locator(f"[data-element-id='{action_args['id']}']")
            if element:
                await element.click()
            else:
                raise ValueError(f"Element with ID {action_args['id']} not found")
            return f"I clicked on {action_args['id']}"
        elif action == "type":
            await page.wait_for_selector(f"[data-element-id='{action_args['id']}']")
            element = page.locator(f"[data-element-id='{action_args['id']}']")
            if element:
                await element.fill(action_args["text"])
                # Press enter
                await element.press("Enter")
            else:
                raise ValueError(f"Element with ID {action_args['id']} not found")
            return f"I typed {action_args['text']} into {action_args['id']}"
        elif action == "scroll":
            await page.scroll(action_args["direction"])
            return f"I scrolled {action_args['direction']}"
        elif action == "stop_action":
            return action_args["final_answer"]
        else:
            raise ValueError(f"Invalid action: {action}")

    def _get_llm_response(
        self,
        client: OpenAI,  # OpenAI client
        task: str,  # Task to complete
        page_text: str,  # Page text
        formatted_list_of_elements: str,  # Formatted list of elements
        image_data_url: str,  # Image data URL
        message_history: list[dict] = [],  # Message history
        model: str = "gpt-4o",  # Model to use
    ) -> dict:
        response = client.chat.completions.create(
            model=model,
            messages=[
                *message_history,
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": AGENT_PROMPT.format(
                                task=task,
                                page_text=page_text,
                                formatted_list_of_elements=formatted_list_of_elements,
                            ),
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": image_data_url},
                        },
                    ],
                },
            ],
        )

        # Parse the response
        try:
            action_decision = json.loads(response.choices[0].message.content)
        except json.JSONDecodeError:
            # it starts with ```json
            response_content = response.choices[0].message.content
            response_content = response_content.replace("```json", "").replace(
                "```", ""
            )
            action_decision = json.loads(response_content)
        except Exception as e:
            raise e
        return action_decision

    async def _prepare_page_for_agent(self, page: Page) -> tuple[str, str, Image.Image]:
        """
        Prepare the page for the agent.
        Returns:
            tuple[str, str, Image.Image]: The page text, the formatted list of elements, and the screenshot with bounding boxes.
        """
        page_text = await page.evaluate("() => document.body.innerText")
        elements = await self._get_interactive_elements(page)
        screenshot_bytes = await page.screenshot()
        som_screenshot = self._get_som_screenshot(screenshot_bytes, elements)

        formatted_list_of_elements = "\n".join(
            [f"Element {i}: {element}" for i, element in enumerate(elements)]
        )

        return page_text, formatted_list_of_elements, som_screenshot

    def _get_som_screenshot(
        self, screenshot_bytes: bytes, elements: list[Element]
    ) -> Image.Image:
        screenshot = Image.open(io.BytesIO(screenshot_bytes))

        # Create a drawing object
        draw = ImageDraw.Draw(screenshot)

        # Draw bounding boxes and element IDs for each element
        for element in elements:
            bbox = element.bbox
            x = bbox["x"]
            y = bbox["y"]
            width = bbox["width"]
            height = bbox["height"]

            # Draw rectangle
            draw.rectangle([(x, y), (x + width, y + height)], outline="red", width=2)

            # Draw element ID
            draw.text((x, y - 15), f"{element.id}", fill="red")

        som_screenshot = screenshot.copy()
        return som_screenshot

    async def _get_interactive_elements(self, page: Page) -> list[Element]:
        elements: list[Element] = []
        # Viewport size is a dict with keys 'width' and 'height'
        viewport_size = page.viewport_size

        # For simplicity, we will only look at buttons, textboxes, and links. We can add more roles later on.
        interactive_roles = ["button", "textbox", "link"]
        i = 0
        for role in interactive_roles:
            # We will use the Playwright API to get all elements with the given role
            elements_with_role = await page.get_by_role(role).all()
            for element in elements_with_role:
                # Check if element is visible and in current viewport
                bbox = await element.bounding_box()
                if bbox:  # Element is visible if it has a bounding box
                    # Check if element is in current viewport (not scrolled out of view)
                    if 0 <= bbox["y"] <= viewport_size["height"]:
                        # Set a data attribute with the element ID for later reference
                        await element.evaluate(
                            f"el => el.setAttribute('data-element-id', '{i}')"
                        )
                        elements.append(
                            Element(
                                id=i,
                                aria_label=await element.get_attribute("aria-label")
                                or await element.get_attribute("aria-role")
                                or "",
                                type=role,
                                bbox=bbox,
                                text=await element.text_content() or "",
                            )
                        )
                        i += 1
        return elements

    async def close(self) -> None:
        if self.page is not None:
            await self.page.close()
        if self.context is not None:
            await self.context.close()
        if self.browser is not None:
            await self.browser.close()

from openai import OpenAI
import os
openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
agent = BrowserUseAgent(openai_client)
try:
    final_answer = await agent.execute_task("find the open issues assigned to husseinmozannar on the microsoft/magentic-ui repo on github")
    print(final_answer)
finally:
    await agent.close()

from magentic_ui.tools.playwright import LocalPlaywrightBrowser
browser = LocalPlaywrightBrowser(headless=False)
# Start the browser
await browser._start()
# Get the browser context and start a new page
context = browser.browser_context
page = await context.new_page()


from magentic_ui.tools.playwright import PlaywrightController
browser_controller = PlaywrightController(viewport_width=1280, viewport_height=720)

_ = await browser_controller.visit_page(page, "https://www.bing.com")

interactive_elements = await browser_controller.get_interactive_rects(page)
# print the first 20 interactive elements
i = 0
for element in interactive_elements:
    print(f"Element {i}: id={element}, data={interactive_elements[element]}")
    i += 1
    if i > 20:
        break


await browser_controller.fill_id(page, "22", "Magentic-UI")


from PIL import Image
import io
from IPython.display import display

screenshot = await browser_controller.get_screenshot(page)
image = Image.open(io.BytesIO(screenshot))
display(image)


page_text = await browser_controller.get_page_markdown(page)
print(page_text)

from magentic_ui.agents.web_surfer._set_of_mark import add_set_of_mark


interactive_elements = await browser_controller.get_interactive_rects(page)
screenshot = await browser_controller.get_screenshot(page)
som_screenshot, visible_elements, elements_above, elements_below, _ = add_set_of_mark(
    screenshot, interactive_elements, use_sequential_ids=True
)

display(som_screenshot)


from openai import OpenAI
from playwright.async_api import Page
from playwright.async_api import async_playwright
from PIL import Image, ImageDraw
import io
import base64
import json
from dataclasses import dataclass
from IPython.display import display
from magentic_ui.tools.playwright import LocalPlaywrightBrowser
from magentic_ui.tools.playwright import PlaywrightController
from magentic_ui.agents.web_surfer._set_of_mark import add_set_of_mark


AGENT_PROMPT = """
You are a helpful assistant that can navigate a web page and perform actions on it.

The task we are trying to complete is:
{task}

The current visible text on the page is:
{page_text}

The current visible elements on the page are:
{formatted_list_of_elements}

You will need to decide on the next action to take.

The action space is:
- goto(url): navigate to a URL
- click(id): click a button given it's ID
- type(id, text): type "text" into element "id"
- scroll(direction): scroll the page in direction up or down.
- stop_action(final_answer): declare that we have finished the task and prepare a final_answer to return to the user.

Output a JSON object with the following fields:
{{
    "action": "goto" | "click" | "type" | "scroll" | "stop_action",
    "action_args": {{
        "url": "https://www.google.com",
        "id": "123",
        "text": "Hello",
        "direction": "up"
    }}
}}

Only output the JSON object, no other text or comments.
"""


class BrowserUseAgent:
    def __init__(
        self,
        client: OpenAI,
        model: str = "gpt-4o",
        headless: bool = False,
        run_in_jupyter: bool = True,
    ):
        self.client = client
        self.model = model
        self.headless = headless
        self.message_history: list[dict] = []
        self.page: Page = None
        self.run_in_jupyter = run_in_jupyter
        self.browser_controller = PlaywrightController(
            viewport_width=1280, viewport_height=720
        )

    async def _launch_browser(self) -> None:
        self.browser = LocalPlaywrightBrowser(headless=False)
        # Start the browser
        await self.browser._start()
        # Get the browser context and start a new page
        self.context = self.browser.browser_context
        self.page = await self.context.new_page()

    async def execute_task(self, task: str) -> str:
        """
        This is NEW! This is the main function that will be called to execute the task and implement our agent loop.
        """
        # Step 1: Launch the browser if it's not already launched
        if self.page is None:
            await self._launch_browser()
        # Our stop condition is when the LLM decides to output stop_action
        should_stop = False
        final_answer = None
        i = 0
        while not should_stop:
            # Step 2: Prepare the page for the agent
            (
                page_text,
                formatted_list_of_elements,
                som_screenshot,
            ) = await self._prepare_page_for_agent(self.page)
            # Step 3: Get the LLM response
            image_data_url = f"data:image/png;base64,{base64.b64encode((lambda b: (som_screenshot.save(b, format='PNG'), b.getvalue())[1])(io.BytesIO())).decode()}"
            action_decision = self._get_llm_response(
                self.client,
                task,
                page_text,
                formatted_list_of_elements,
                image_data_url,
                self.message_history,
                self.model,
            )
            print(f"Action decision {i}: {action_decision}")
            # Add the action decision to the message history
            self.message_history.append(
                {
                    "role": "user",
                    "content": [{"type": "text", "text": json.dumps(action_decision)}],
                }
            )
            # Step 4: Execute the action with some error handling
            try:
                action_feedback = await self._execute_action(
                    action_decision["action"], action_decision["action_args"], self.page
                )
            except Exception as e:
                print(f"Error executing action {i}: {e}")
                action_feedback = f"Error executing action {i}: {e}"
            print(f"Action feedback {i}: {action_feedback}")
            # Sleep for 3 seconds to let the page load
            await self.page.wait_for_timeout(3000)
            # Update the message history with feedback on the action and the new page screenshot
            new_page_screenshot = await self.page.screenshot()
            self.message_history.append(
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": action_feedback},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64.b64encode(new_page_screenshot).decode()}"
                            },
                        },
                    ],
                }
            )
            if self.run_in_jupyter:
                display(Image.open(io.BytesIO(new_page_screenshot)))
            # Check if the task is complete
            should_stop = action_decision["action"] == "stop_action"
            if should_stop:
                final_answer = action_decision["action_args"]["final_answer"]
            i += 1
        return final_answer

    async def _prepare_page_for_agent(self, page: Page) -> tuple[str, str, bytes]:
        interactive_elements = await self.browser_controller.get_interactive_rects(page)
        screenshot = await self.browser_controller.get_screenshot(page)
        som_screenshot, visible_elements, elements_above, elements_below, _ = (
            add_set_of_mark(screenshot, interactive_elements, use_sequential_ids=False)
        )
        visible_elements_formatted = ""
        for element_id in visible_elements:
            element_data = interactive_elements[element_id]
            visible_elements_formatted += f"{element_id}: {element_data}\n"

        page_text = await self.browser_controller.get_page_markdown(page)
        return page_text, visible_elements_formatted, som_screenshot
    async def _execute_action(self, action: str, action_args: dict, page: Page) -> str:
        if action == "goto":
            await self.browser_controller.visit_page(page, action_args["url"])
            return f"Visited {action_args['url']}"
        elif action == "click":
            await self.browser_controller.click_id(self.context, page, action_args["id"])
            return f"Clicked {action_args['id']}"
        elif action == "type":
            await self.browser_controller.fill_id(page, action_args["id"], action_args["text"])
            return f"Typed {action_args['text']} into {action_args['id']}"
        elif action == "scroll":
            if action_args["direction"] == "up":
                await self.browser_controller.page_up(page)
            elif action_args["direction"] == "down":
                await self.browser_controller.page_down(page)
            return f"Scrolled {action_args['direction']}"
        elif action == "stop_action":
            return action_args["final_answer"]
        else:
            raise ValueError(f"Invalid action: {action}")

    def _get_llm_response(
        self,
        client: OpenAI,  # OpenAI client
        task: str,  # Task to complete
        page_text: str,  # Page text
        formatted_list_of_elements: str,  # Formatted list of elements
        image_data_url: str,  # Image data URL
        message_history: list[dict] = [],  # Message history
        model: str = "gpt-4o",  # Model to use
    ) -> dict:
        response = client.chat.completions.create(
            model=model,
            messages=[
                *message_history,
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": AGENT_PROMPT.format(
                                task=task,
                                page_text=page_text,
                                formatted_list_of_elements=formatted_list_of_elements,
                            ),
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": image_data_url},
                        },
                    ],
                },
            ],
        )

        # Parse the response
        try:
            action_decision = json.loads(response.choices[0].message.content)
        except json.JSONDecodeError:
            # it starts with ```json
            response_content = response.choices[0].message.content
            response_content = response_content.replace("```json", "").replace(
                "```", ""
            )
            action_decision = json.loads(response_content)
        except Exception as e:
            raise e
        return action_decision

    async def close(self) -> None:
        if self.page is not None:
            await self.page.close()
        if self.context is not None:
            await self.context.close()


from openai import OpenAI
import os
openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
agent = BrowserUseAgent(openai_client)
try:
    final_answer = await agent.execute_task("find the open issues assigned to husseinmozannar on the microsoft/magentic-ui repo on github")
    print(final_answer)
finally:
    await agent.close()


from autogen_ext.models.openai import OpenAIChatCompletionClient
from magentic_ui.agents import WebSurfer
from magentic_ui.tools.playwright import (
    LocalPlaywrightBrowser,
)

browser = LocalPlaywrightBrowser(headless=False)

model_client = OpenAIChatCompletionClient(model="gpt-4o")

web_surfer = WebSurfer(
    name="web_surfer",
    model_client=model_client, # Use any client from AutoGen!
    animate_actions=True, # Set to True if you want to see the actions being animated!
    max_actions_per_step=10, # Maximum number of actions to perform before returning
    downloads_folder="debug", # Where to save downloads
    debug_dir="debug", # Where to save debug files and screenshots
    to_save_screenshots=False, # set to True if you want to save screenshots of the actions
    browser=browser, # Use any browser from Magentic-UI!
    multiple_tools_per_call=False, # Set to True if you want to use multiple tools per call
    json_model_output=False, # Set to True if your model does not support tool calling
)
await web_surfer.lazy_init()

task = "find the open issues assigned to husseinmozannar on the microsoft/magentic-ui repo on github"
try:
    messages = []
    async for message in web_surfer.run_stream(task=task):
        messages.append(message)
        print(message)
    print("########################################################")
    print("Final answer:")
    print(messages[-1].messages[-2].content)
finally:
    await web_surfer.close()
